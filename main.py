"""
OpenManus 主程序入口文件

这是 OpenManus 项目的主要启动文件，用于创建和运行 Manus 智能体。
OpenManus 是一个开源的通用 AI 智能体框架，支持多种工具和 MCP 协议。

主要功能：
1. 解析命令行参数
2. 创建并初始化 Manus 智能体
3. 处理用户输入的提示词
4. 运行智能体并处理任务
5. 清理资源

使用方法：
    python main.py                    # 交互式输入提示词
    python main.py --prompt "任务描述"  # 直接指定提示词

作者：OpenManus 团队
项目地址：https://github.com/FoundationAgents/OpenManus
"""

import argparse
import asyncio

from app.agent.manus import Manus
from app.logger import logger


async def main():
    """
    主异步函数，负责整个程序的执行流程

    执行步骤：
    1. 解析命令行参数
    2. 创建 Manus 智能体实例
    3. 获取用户输入的提示词
    4. 运行智能体处理任务
    5. 清理资源
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行 Manus 智能体处理用户提示")
    parser.add_argument(
        "--prompt",
        type=str,
        required=False,
        help="智能体的输入提示词，如果不提供则会交互式询问"
    )
    args = parser.parse_args()

    # 创建并初始化 Manus 智能体
    # Manus 是一个通用智能体，支持多种工具和 MCP 协议
    agent = await Manus.create()

    try:
        # 获取提示词：优先使用命令行参数，否则交互式输入
        prompt = args.prompt if args.prompt else input("请输入您的任务描述: ")

        # 检查提示词是否为空
        if not prompt.strip():
            logger.warning("提示词为空，程序退出。")
            return

        # 开始处理用户请求
        logger.warning("正在处理您的请求...")
        await agent.run(prompt)
        logger.info("请求处理完成。")

    except KeyboardInterrupt:
        # 处理用户中断（Ctrl+C）
        logger.warning("操作被用户中断。")

    finally:
        # 确保在程序退出前清理智能体资源
        # 包括关闭浏览器、断开 MCP 连接等
        await agent.cleanup()


if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
