#!/usr/bin/env python
"""
OpenManus MCP 智能体运行器

这个文件提供了运行 MCP (Model Context Protocol) 智能体的入口。
MCP 是一个标准化协议，允许智能体与外部工具和服务进行通信。

主要功能：
1. 支持 stdio 和 SSE 两种连接方式
2. 提供交互式和单次执行两种运行模式
3. 自动处理 MCP 服务器连接和资源清理
4. 支持命令行参数配置

连接类型：
- stdio: 通过标准输入输出与 MCP 服务器通信
- sse: 通过 Server-Sent Events 与 MCP 服务器通信

使用方法：
    python run_mcp.py                           # 默认模式，stdio 连接
    python run_mcp.py -i                        # 交互式模式
    python run_mcp.py -p "任务描述"               # 单次执行
    python run_mcp.py -c sse --server-url URL   # 使用 SSE 连接

作者：OpenManus 团队
"""

import argparse
import asyncio
import sys

from app.agent.mcp import MCPAgent
from app.config import config
from app.logger import logger


class MCPRunner:
    """
    MCP 智能体运行器类

    负责管理 MCP 智能体的生命周期，包括初始化、运行和清理。
    支持多种连接方式和运行模式。
    """

    def __init__(self):
        """
        初始化 MCP 运行器

        从配置中获取根路径和服务器引用，创建 MCP 智能体实例。
        """
        self.root_path = config.root_path
        self.server_reference = config.mcp_config.server_reference
        self.agent = MCPAgent()

    async def initialize(
        self,
        connection_type: str,
        server_url: str | None = None,
    ) -> None:
        """
        初始化 MCP 智能体连接

        Args:
            connection_type: 连接类型，'stdio' 或 'sse'
            server_url: SSE 连接时的服务器 URL（可选）
        """
        logger.info(f"正在使用 {connection_type} 连接初始化 MCP 智能体...")

        if connection_type == "stdio":
            # 使用标准输入输出连接 MCP 服务器
            await self.agent.initialize(
                connection_type="stdio",
                command=sys.executable,
                args=["-m", self.server_reference],
            )
        else:  # sse
            # 使用 Server-Sent Events 连接 MCP 服务器
            await self.agent.initialize(connection_type="sse", server_url=server_url)

        logger.info(f"已通过 {connection_type} 连接到 MCP 服务器")

    async def run_interactive(self) -> None:
        """
        以交互式模式运行智能体

        用户可以持续输入请求，智能体会逐一处理。
        输入 'exit'、'quit' 或 'q' 退出。
        """
        print("\nMCP 智能体交互模式 (输入 'exit' 退出)\n")
        while True:
            user_input = input("\n请输入您的请求: ")
            if user_input.lower() in ["exit", "quit", "q"]:
                break
            response = await self.agent.run(user_input)
            print(f"\n智能体: {response}")

    async def run_single_prompt(self, prompt: str) -> None:
        """
        使用单个提示词运行智能体

        Args:
            prompt: 用户输入的提示词
        """
        await self.agent.run(prompt)

    async def run_default(self) -> None:
        """
        以默认模式运行智能体

        交互式获取用户输入，然后执行单次任务。
        """
        prompt = input("请输入您的任务描述: ")
        if not prompt.strip():
            logger.warning("提示词为空，程序退出。")
            return

        logger.warning("正在处理您的请求...")
        await self.agent.run(prompt)
        logger.info("请求处理完成。")

    async def cleanup(self) -> None:
        """
        清理智能体资源

        关闭 MCP 连接，释放相关资源。
        """
        await self.agent.cleanup()
        logger.info("会话结束")


def parse_args() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(description="运行 MCP 智能体")
    parser.add_argument(
        "--connection",
        "-c",
        choices=["stdio", "sse"],
        default="stdio",
        help="连接类型: stdio（标准输入输出）或 sse（Server-Sent Events）",
    )
    parser.add_argument(
        "--server-url",
        default="http://127.0.0.1:8000/sse",
        help="SSE 连接的服务器 URL",
    )
    parser.add_argument(
        "--interactive",
        "-i",
        action="store_true",
        help="以交互式模式运行"
    )
    parser.add_argument(
        "--prompt",
        "-p",
        help="单次执行的提示词，执行后退出"
    )
    return parser.parse_args()


async def run_mcp() -> None:
    """
    MCP 运行器的主入口点

    根据命令行参数初始化并运行 MCP 智能体。
    处理不同的运行模式和异常情况。
    """
    args = parse_args()
    runner = MCPRunner()

    try:
        # 初始化 MCP 连接
        await runner.initialize(args.connection, args.server_url)

        # 根据参数选择运行模式
        if args.prompt:
            # 单次执行模式
            await runner.run_single_prompt(args.prompt)
        elif args.interactive:
            # 交互式模式
            await runner.run_interactive()
        else:
            # 默认模式
            await runner.run_default()

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"运行 MCP 智能体时出错: {str(e)}", exc_info=True)
        sys.exit(1)
    finally:
        # 确保资源被正确清理
        await runner.cleanup()


if __name__ == "__main__":
    # 运行 MCP 智能体
    asyncio.run(run_mcp())
